# docker-compose.yml - Production AI Agent Stack
version: '3.8'

services:
  # ========== DATABASE SERVICES ==========
  postgres:
    image: pgvector/pgvector:pg16
    container_name: ai-agent-postgres
    environment:
      POSTGRES_DB: agent_db
      POSTGRES_USER: agent_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/config/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent_user -d agent_db"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    networks:
      - agent-network

  # ========== REDIS CACHE SERVICE ==========
  redis:
    image: redis:7.2-alpine
    container_name: ai-agent-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    networks:
      - agent-network

  # ========== MAIN AGENT SERVICE ==========
  agent-service:
    build:
      context: .
      dockerfile: services/agent/Dockerfile
      target: production
      args:
        - PYTHON_VERSION=3.11
        - BUILD_DATE=${BUILD_DATE}
        - GIT_COMMIT=${GIT_COMMIT}
    container_name: ai-agent-service
    environment:
      - DATABASE_URL=postgresql://agent_user:${POSTGRES_PASSWORD}@postgres:5432/agent_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
      - AGENT_WORKER_PROCESSES=4
      - AGENT_WORKER_THREADS=8
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models:ro
      - agent_checkpoints:/app/checkpoints
    ports:
      - "8000:8000"
      - "8001:8001"  # Health check port
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    networks:
      - agent-network

  # ========== LANGGRAPH ORCHESTRATOR ==========
  langgraph-orchestrator:
    build:
      context: .
      dockerfile: services/orchestrator/Dockerfile
      target: production
    container_name: langgraph-orchestrator
    environment:
      - DATABASE_URL=postgresql://agent_user:${POSTGRES_PASSWORD}@postgres:5432/agent_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/1
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - ORCHESTRATOR_WORKERS=4
      - MAX_CONCURRENT_AGENTS=20
    volumes:
      - ./logs:/app/logs
      - orchestrator_state:/app/state
    ports:
      - "8002:8002"
    depends_on:
      - postgres
      - redis
      - agent-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '3.0'
        reservations:
          memory: 2G
          cpus: '1.5'
    networks:
      - agent-network

  # ========== MEMORY SERVICE ==========
  memory-service:
    build:
      context: .
      dockerfile: services/memory/Dockerfile
      target: production
    container_name: memory-service
    environment:
      - DATABASE_URL=postgresql://agent_user:${POSTGRES_PASSWORD}@postgres:5432/agent_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/2
      - EMBEDDING_BATCH_SIZE=32
      - VECTOR_DIMENSION=1536
      - MEMORY_CONSOLIDATION_INTERVAL=3600
    volumes:
      - ./logs:/app/logs
      - memory_embeddings:/app/embeddings
    ports:
      - "8003:8003"
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    networks:
      - agent-network

  # ========== MESSAGE QUEUE ==========
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: ai-agent-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_DEFAULT_VHOST: agent_vhost
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
    networks:
      - agent-network

  # ========== CELERY WORKER ==========
  celery-worker:
    build:
      context: .
      dockerfile: services/worker/Dockerfile
      target: production
    container_name: celery-worker
    environment:
      - DATABASE_URL=postgresql://agent_user:${POSTGRES_PASSWORD}@postgres:5432/agent_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/3
      - RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/agent_vhost
      - CELERY_WORKERS=4
      - CELERY_MAX_TASKS_PER_CHILD=1000
    volumes:
      - ./logs:/app/logs
      - worker_temp:/app/temp
    depends_on:
      - postgres
      - redis
      - rabbitmq
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    networks:
      - agent-network

  # ========== LOAD BALANCER ==========
  nginx:
    image: nginx:1.25-alpine
    container_name: ai-agent-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - agent-service
      - langgraph-orchestrator
      - memory-service
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    networks:
      - agent-network

  # ========== MONITORING STACK ==========
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=90d'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - agent-network

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - agent-network

  # ========== LOG AGGREGATION ==========
  fluentd:
    build:
      context: ./monitoring/fluentd
      dockerfile: Dockerfile
    container_name: fluentd
    volumes:
      - ./logs:/fluentd/log
      - ./monitoring/fluentd/conf:/fluentd/etc
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    networks:
      - agent-network

# ========== NETWORKS ==========
networks:
  agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ========== VOLUMES ==========
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  agent_checkpoints:
    driver: local
  orchestrator_state:
    driver: local
  memory_embeddings:
    driver: local
  worker_temp:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local