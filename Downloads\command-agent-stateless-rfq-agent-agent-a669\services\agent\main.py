"""
Main FastAPI application for LangGraph AI Agent Service
Implements 12-Factor Agent principles with enterprise-grade features
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import structlog
import sys
from typing import Dict, Any, Optional
import os
import asyncio
from datetime import datetime

from .core.config import get_settings
from .core.database import get_database, init_database
from .core.redis_client import get_redis, init_redis
from .core.monitoring import setup_monitoring, get_metrics
from .agents.rfq_agent import RFQWorkflowAgent
from .schemas.rfq import RFQRequest, RFQResponse
from .services.memory_service import MemoryService
from .services.prompt_service import PromptService
from .services.tool_service import ToolService
from .services.universal_input_handler import UniversalInputHandler
from .services.stateless_reducer import StatelessAgentService
from .services.functional_workflow_service import FunctionalWorkflowService
from .api.input_gateway import create_input_gateway_routes
from .middleware.error_handler import ErrorHandlerMiddleware
from .middleware.rate_limiter import RateLimiterMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown"""
    logger.info("Starting AI Agent Service")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        # Initialize Redis
        await init_redis()
        logger.info("Redis initialized")
        
        # Setup monitoring
        setup_monitoring(app)
        logger.info("Monitoring setup complete")
        
        # Initialize services
        app.state.memory_service = MemoryService()
        app.state.prompt_service = PromptService()
        app.state.tool_service = ToolService()
        app.state.rfq_agent = RFQWorkflowAgent()
        app.state.universal_input_handler = UniversalInputHandler()
        app.state.stateless_service = StatelessAgentService()
        app.state.functional_workflow = FunctionalWorkflowService()
        
        # Configure Factor 11: Universal Input Gateway routes
        create_input_gateway_routes(app, app.state.universal_input_handler)
        
        logger.info("AI Agent Service started successfully")
        yield
        
    except Exception as e:
        logger.error("Failed to start AI Agent Service", error=str(e))
        sys.exit(1)
    finally:
        logger.info("Shutting down AI Agent Service")

# Create FastAPI app
app = FastAPI(
    title="LangGraph AI Agent Service",
    description="Enterprise AI Agent System with 12-Factor principles",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") == "development" else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

app.add_middleware(ErrorHandlerMiddleware)
app.add_middleware(RateLimiterMiddleware)

# ========== FACTOR 11: UNIVERSAL INPUT GATEWAY ==========
# Initialize input gateway routes after app startup
# This will be configured in the lifespan after services are initialized

# ========== HEALTH CHECKS ==========

@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer"""
    try:
        # Check database
        db = await get_database()
        await db.execute("SELECT 1")
        
        # Check Redis
        redis = await get_redis()
        await redis.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "services": {
                "database": "healthy",
                "redis": "healthy",
                "memory_service": "healthy"
            }
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/ready")
async def readiness_check():
    """Readiness check for Kubernetes"""
    return {
        "status": "ready",
        "timestamp": datetime.utcnow().isoformat()
    }

# ========== METRICS ==========

@app.get("/metrics")
async def get_prometheus_metrics():
    """Prometheus metrics endpoint"""
    return get_metrics()

# ========== MAIN RFQ WORKFLOW ENDPOINT ==========

@app.post("/api/rfq/process", response_model=RFQResponse)
async def process_rfq(
    request: RFQRequest,
    background_tasks: BackgroundTasks,
    db = Depends(get_database),
    redis = Depends(get_redis)
):
    """
    Process RFQ request using LangGraph workflow
    Implements Factor 1: Natural Language to Tool Calls
    """
    logger.info("Processing RFQ request", 
                request_id=request.request_id, 
                user_id=request.user_id)
    
    try:
        # Get agent instance
        rfq_agent = app.state.rfq_agent
        
        # Process the RFQ workflow
        result = await rfq_agent.process_rfq_workflow(
            input_data=request.model_dump(),
            user_id=request.user_id,
            db=db,
            redis=redis
        )
        
        # Schedule background memory consolidation
        background_tasks.add_task(
            consolidate_memory_background,
            execution_id=result.execution_id
        )
        
        logger.info("RFQ processed successfully", 
                   execution_id=result.execution_id,
                   rfq_id=result.rfq_state.rfq_id)
        
        return result
        
    except Exception as e:
        logger.error("RFQ processing failed", 
                    error=str(e), 
                    request_id=request.request_id)
        raise HTTPException(
            status_code=500,
            detail=f"RFQ processing failed: {str(e)}"
        )

# ========== WORKFLOW CONTROL ENDPOINTS (Factor 6: Launch/Pause/Resume) ==========

@app.post("/api/workflows/{execution_id}/pause")
async def pause_workflow(
    execution_id: str,
    db = Depends(get_database)
):
    """Pause a running workflow"""
    try:
        rfq_agent = app.state.rfq_agent
        result = await rfq_agent.pause_workflow(execution_id, db)
        return {"status": "paused", "execution_id": execution_id}
    except Exception as e:
        logger.error("Failed to pause workflow", 
                    execution_id=execution_id, 
                    error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/workflows/{execution_id}/resume")
async def resume_workflow(
    execution_id: str,
    db = Depends(get_database)
):
    """Resume a paused workflow"""
    try:
        rfq_agent = app.state.rfq_agent
        result = await rfq_agent.resume_workflow(execution_id, db)
        return {"status": "resumed", "execution_id": execution_id}
    except Exception as e:
        logger.error("Failed to resume workflow", 
                    execution_id=execution_id, 
                    error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/workflows/{execution_id}/status")
async def get_workflow_status(
    execution_id: str,
    db = Depends(get_database)
):
    """Get workflow execution status"""
    try:
        rfq_agent = app.state.rfq_agent
        status = await rfq_agent.get_workflow_status(execution_id, db)
        return status
    except Exception as e:
        logger.error("Failed to get workflow status", 
                    execution_id=execution_id, 
                    error=str(e))
        raise HTTPException(status_code=404, detail="Workflow not found")

# ========== HUMAN-IN-THE-LOOP ENDPOINTS (Factor 7) ==========

@app.get("/api/human-tasks")
async def get_pending_human_tasks(
    user_id: Optional[str] = None,
    db = Depends(get_database)
):
    """Get pending human tasks for approval"""
    try:
        # Implementation for getting human tasks
        query = """
        SELECT id, execution_id, task_type, title, description, 
               context_data, priority, created_at, deadline
        FROM agent_workflows.human_tasks 
        WHERE status = 'pending'
        """
        if user_id:
            query += " AND assigned_to = :user_id"
        
        query += " ORDER BY priority DESC, created_at ASC"
        
        if user_id:
            result = await db.fetch_all(query, {"user_id": user_id})
        else:
            result = await db.fetch_all(query)
        
        return {"tasks": [dict(row) for row in result]}
    except Exception as e:
        logger.error("Failed to get human tasks", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/human-tasks/{task_id}/respond")
async def respond_to_human_task(
    task_id: str,
    response_data: Dict[str, Any],
    db = Depends(get_database)
):
    """Respond to a human task"""
    try:
        # Update task with response
        query = """
        UPDATE agent_workflows.human_tasks 
        SET status = 'completed', response_data = :response_data, 
            completed_at = NOW()
        WHERE id = :task_id
        RETURNING execution_id
        """
        
        result = await db.fetch_one(query, {
            "task_id": task_id,
            "response_data": response_data
        })
        
        if not result:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Resume the workflow if it was waiting for this response
        execution_id = result["execution_id"]
        rfq_agent = app.state.rfq_agent
        await rfq_agent.handle_human_response(execution_id, task_id, response_data, db)
        
        return {"status": "completed", "task_id": task_id}
        
    except Exception as e:
        logger.error("Failed to respond to human task", 
                    task_id=task_id, 
                    error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# ========== MEMORY ENDPOINTS (Factor 3) ==========

@app.post("/api/memory/search")
async def search_memory(
    query: str,
    limit: int = 10,
    db = Depends(get_database)
):
    """Search semantic memory using vector similarity"""
    try:
        memory_service = app.state.memory_service
        results = await memory_service.search_memories(query, limit, db)
        return {"results": results}
    except Exception as e:
        logger.error("Memory search failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/memory/consolidate")
async def trigger_memory_consolidation(
    background_tasks: BackgroundTasks,
    db = Depends(get_database)
):
    """Trigger memory consolidation process"""
    background_tasks.add_task(consolidate_memory_background, None)
    return {"status": "consolidation_scheduled"}

# ========== PROMPT MANAGEMENT (Factor 2) ==========

@app.get("/api/prompts")
async def get_prompts(db = Depends(get_database)):
    """Get all active prompts"""
    try:
        prompt_service = app.state.prompt_service
        prompts = await prompt_service.get_active_prompts(db)
        return {"prompts": prompts}
    except Exception as e:
        logger.error("Failed to get prompts", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/prompts")
async def create_prompt(
    prompt_data: Dict[str, Any],
    db = Depends(get_database)
):
    """Create a new prompt version"""
    try:
        prompt_service = app.state.prompt_service
        prompt = await prompt_service.create_prompt(prompt_data, db)
        return prompt
    except Exception as e:
        logger.error("Failed to create prompt", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# ========== TOOL REGISTRY (Factor 4) ==========

@app.get("/api/tools")
async def get_tools(db = Depends(get_database)):
    """Get all registered tools"""
    try:
        tool_service = app.state.tool_service
        tools = await tool_service.get_active_tools(db)
        return {"tools": tools}
    except Exception as e:
        logger.error("Failed to get tools", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# ========== BACKGROUND TASKS ==========

async def consolidate_memory_background(execution_id: Optional[str]):
    """Background task for memory consolidation"""
    try:
        logger.info("Starting memory consolidation", execution_id=execution_id)
        
        db = await get_database()
        memory_service = MemoryService()
        
        await memory_service.consolidate_memories(execution_id, db)
        
        logger.info("Memory consolidation completed", execution_id=execution_id)
    except Exception as e:
        logger.error("Memory consolidation failed", 
                    execution_id=execution_id, 
                    error=str(e))

# ========== FACTOR 12: STATELESS REDUCER ENDPOINTS ==========

@app.post("/api/functional/rfq")
async def execute_functional_rfq_workflow(
    request_data: Dict[str, Any],
    db = Depends(get_database)
):
    """Execute RFQ workflow using functional patterns (Factor 12)"""
    try:
        functional_service = app.state.functional_workflow
        
        rfq_request = request_data.get("rfq_request", "")
        user_id = request_data.get("user_id", "system")
        
        result = await functional_service.execute_rfq_workflow(rfq_request, user_id)
        
        return result
        
    except Exception as e:
        logger.error("Functional RFQ workflow failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/functional/parallel-rfq")
async def execute_parallel_functional_workflow(
    request_data: Dict[str, Any],
    db = Depends(get_database)
):
    """Execute parallel RFQ workflow using functional patterns"""
    try:
        functional_service = app.state.functional_workflow
        
        rfq_request = request_data.get("rfq_request", "")
        
        result = await functional_service.execute_parallel_workflow_branches(rfq_request)
        
        return result
        
    except Exception as e:
        logger.error("Parallel functional workflow failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/functional/capabilities")
async def get_functional_capabilities():
    """Get functional service capabilities"""
    try:
        stateless_service = app.state.stateless_service
        functional_service = app.state.functional_workflow
        
        return {
            "stateless_reducer": stateless_service.get_service_capabilities(),
            "functional_workflow": functional_service.get_service_capabilities(),
            "factor_compliance": "Factor 12: Make Your Agent a Stateless Reducer",
            "functional_principles": [
                "Immutable state management",
                "Pure function operations", 
                "Predictable state transitions",
                "Functional composition",
                "Side effect isolation"
            ]
        }
        
    except Exception as e:
        logger.error("Failed to get functional capabilities", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# ========== ERROR HANDLERS ==========

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler with error compaction (Factor 9)"""
    logger.error("Unhandled exception", 
                path=request.url.path,
                error=str(exc),
                exc_info=True)
    
    # Compact error for context window
    error_summary = f"Error in {request.url.path}: {str(exc)[:200]}..."
    
    return HTTPException(
        status_code=500,
        detail="Internal server error occurred"
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if os.getenv("ENVIRONMENT") == "development" else False
    )