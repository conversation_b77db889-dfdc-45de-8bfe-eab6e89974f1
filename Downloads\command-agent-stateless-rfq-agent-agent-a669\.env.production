# Environment Configuration for AI Agent System
# Copy this to .env and fill in the actual values

# ========== DATABASE CONFIGURATION ==========
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_DB=agent_db
POSTGRES_USER=agent_user

# ========== REDIS CONFIGURATION ==========
REDIS_PASSWORD=your_secure_redis_password_here

# ========== MESSAGE QUEUE CONFIGURATION ==========
RABBITMQ_USER=agent_user
RABBITMQ_PASSWORD=your_secure_rabbitmq_password_here

# ========== API KEYS ==========
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# ========== MONITORING ==========
GRAFANA_PASSWORD=your_secure_grafana_password_here

# ========== BUILD CONFIGURATION ==========
BUILD_DATE=2025-01-01T00:00:00Z
GIT_COMMIT=main

# ========== ENVIRONMENT ==========
ENVIRONMENT=production
LOG_LEVEL=INFO

# ========== SECURITY ==========
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# ========== FEATURE FLAGS ==========
ENABLE_HUMAN_IN_LOOP=true
ENABLE_ADVANCED_MEMORY=true
ENABLE_MONITORING=true
ENABLE_ASYNC_PROCESSING=true

# ========== PERFORMANCE TUNING ==========
MAX_CONCURRENT_WORKFLOWS=50
CONTEXT_WINDOW_SIZE=32000
EMBEDDING_BATCH_SIZE=32
MEMORY_CONSOLIDATION_INTERVAL=3600

# ========== NETWORKING ==========
EXTERNAL_HOST=localhost
API_PORT=8000
NGINX_PORT=80
NGINX_SSL_PORT=443